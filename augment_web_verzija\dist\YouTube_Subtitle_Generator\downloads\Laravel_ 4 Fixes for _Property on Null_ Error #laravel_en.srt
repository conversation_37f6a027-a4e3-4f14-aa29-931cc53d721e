1
00:00:00,000 --> 00:00:05,500
In Laravel you have probably seen this error when you reference the object that doesn't exist from relationship.

2
00:00:05,500 --> 00:00:11,400
Like in the task list you reference the user ID which may be null, I will show you 4 ways to fix it.

3
00:00:11,400 --> 00:00:16,700
The first way in Blade is just to provide the default value after question marks like this.

4
00:00:16,700 --> 00:00:20,400
Then you see no error in the table even if the user is empty.

5
00:00:20,400 --> 00:00:25,700
The second way to fix is to use <PERSON><PERSON> helper called optional on the object of the relationship.

6
00:00:25,700 --> 00:00:29,600
It will not show any default value just prevent the error from appearing.

7
00:00:29,700 --> 00:00:36,100
The third option is to use PHP null save operator which is just adding question mark with the relationship.

8
00:00:36,100 --> 00:00:40,400
And it would also work on multiple levels for example user country name.

9
00:00:40,400 --> 00:00:49,200
And finally you can fix that when defining the relationship in the eloquent model in addition to belongs to user you may provide a method called with default.

10
00:00:49,200 --> 00:00:52,700
Laravel will just create an empty object for the user behind the scenes.

11
00:00:52,700 --> 00:00:56,400
Or you may even provide some values for the fields of that relationship.

12
00:00:56,400 --> 00:00:59,200
Then in the table you would see something like this.

