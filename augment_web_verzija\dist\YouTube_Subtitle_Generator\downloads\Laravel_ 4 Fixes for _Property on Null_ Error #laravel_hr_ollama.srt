1
00:00:00,000 --> 00:00:05,500
U Laravelu ste vjerojatno naišli na ovu grešku kada referencirate objekt koji ne postoji iz odnosa.

2
00:00:05,500 --> 00:00:11,400
Kao u zadatku, referencirate na korisnički ID koji može biti prazan, pokazat ću vam 4 načina kako ga riješiti.

3
00:00:11,400 --> 00:00:16,700
Jedan od načina u Blade je da samo pružite podrazumijevanu vrijednost nakon znaka upitnika kao što je ovdje prikazano.

4
00:00:16,700 --> 00:00:20,400
Tada vam se čini da u tablici nema pogrešaka iako je polje za korisnika prazno.

5
00:00:20,400 --> 00:00:25,700
Drugi način za rješavanje je korištenje Laravel helpera pod nazivom "optional" na objektu odnosa.

6
00:00:25,700 --> 00:00:29,600
Prijevod na hrvatski jezik:

7
00:00:29,700 --> 00:00:36,100
Treća opcija je korištenje PHP null spremište operatera, koji je samo dodavanje upitnika s odnosom.

8
00:00:36,100 --> 00:00:40,400
I prevod: I moglo bi se primijeniti i na više razina, primjerice korisničko ime države.

9
00:00:40,400 --> 00:00:49,200
Na kraju, možete popraviti to kada definirate odnos u elokventnom modelu, osim što pripada korisniku, možete dodati metodu naziva s predefiniranom vrijednošću.

10
00:00:49,200 --> 00:00:52,700
Laravel će samo kreirati prazan objekt za korisnika iza kulisa.

11
00:00:52,700 --> 00:00:56,400
Ili možda čak možete dati neke vrijednosti za polja odnosa te relacije.

12
00:00:56,400 --> 00:00:59,200
Tada u stolu možete vidjeti nešto poput ovoga.