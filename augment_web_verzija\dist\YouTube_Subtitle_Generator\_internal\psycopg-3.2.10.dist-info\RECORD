psycopg-3.2.10.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
psycopg-3.2.10.dist-info/METADATA,sha256=Cvd0n6asFl0jD0tM6UFAjTcLd3oZAWl6JwAkaXVuLpY,4592
psycopg-3.2.10.dist-info/RECORD,,
psycopg-3.2.10.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
psycopg-3.2.10.dist-info/licenses/LICENSE.txt,sha256=46mU2C5kSwOnkqkw9XQAJlhBL2JAf1_uCD8lVcXyMRg,7652
psycopg-3.2.10.dist-info/top_level.txt,sha256=npthKx2_OEoahttYqdsIPlRUgjS_bQBmfpDCVxQcPGo,8
psycopg/__init__.py,sha256=XxYHoZPLY-YKDFaHIJ0r0OMCl2C5VZ6ZaPMda12Cx3g,3423
psycopg/__pycache__/__init__.cpython-310.pyc,,
psycopg/__pycache__/_acompat.cpython-310.pyc,,
psycopg/__pycache__/_adapters_map.cpython-310.pyc,,
psycopg/__pycache__/_capabilities.cpython-310.pyc,,
psycopg/__pycache__/_cmodule.cpython-310.pyc,,
psycopg/__pycache__/_column.cpython-310.pyc,,
psycopg/__pycache__/_compat.cpython-310.pyc,,
psycopg/__pycache__/_connection_base.cpython-310.pyc,,
psycopg/__pycache__/_connection_info.cpython-310.pyc,,
psycopg/__pycache__/_conninfo_attempts.cpython-310.pyc,,
psycopg/__pycache__/_conninfo_attempts_async.cpython-310.pyc,,
psycopg/__pycache__/_conninfo_utils.cpython-310.pyc,,
psycopg/__pycache__/_copy.cpython-310.pyc,,
psycopg/__pycache__/_copy_async.cpython-310.pyc,,
psycopg/__pycache__/_copy_base.cpython-310.pyc,,
psycopg/__pycache__/_cursor_base.cpython-310.pyc,,
psycopg/__pycache__/_dns.cpython-310.pyc,,
psycopg/__pycache__/_encodings.cpython-310.pyc,,
psycopg/__pycache__/_enums.cpython-310.pyc,,
psycopg/__pycache__/_oids.cpython-310.pyc,,
psycopg/__pycache__/_pipeline.cpython-310.pyc,,
psycopg/__pycache__/_pipeline_async.cpython-310.pyc,,
psycopg/__pycache__/_pipeline_base.cpython-310.pyc,,
psycopg/__pycache__/_preparing.cpython-310.pyc,,
psycopg/__pycache__/_py_transformer.cpython-310.pyc,,
psycopg/__pycache__/_queries.cpython-310.pyc,,
psycopg/__pycache__/_server_cursor.cpython-310.pyc,,
psycopg/__pycache__/_server_cursor_async.cpython-310.pyc,,
psycopg/__pycache__/_server_cursor_base.cpython-310.pyc,,
psycopg/__pycache__/_struct.cpython-310.pyc,,
psycopg/__pycache__/_tpc.cpython-310.pyc,,
psycopg/__pycache__/_transformer.cpython-310.pyc,,
psycopg/__pycache__/_typeinfo.cpython-310.pyc,,
psycopg/__pycache__/_typemod.cpython-310.pyc,,
psycopg/__pycache__/_tz.cpython-310.pyc,,
psycopg/__pycache__/_wrappers.cpython-310.pyc,,
psycopg/__pycache__/abc.cpython-310.pyc,,
psycopg/__pycache__/adapt.cpython-310.pyc,,
psycopg/__pycache__/client_cursor.cpython-310.pyc,,
psycopg/__pycache__/connection.cpython-310.pyc,,
psycopg/__pycache__/connection_async.cpython-310.pyc,,
psycopg/__pycache__/conninfo.cpython-310.pyc,,
psycopg/__pycache__/copy.cpython-310.pyc,,
psycopg/__pycache__/cursor.cpython-310.pyc,,
psycopg/__pycache__/cursor_async.cpython-310.pyc,,
psycopg/__pycache__/dbapi20.cpython-310.pyc,,
psycopg/__pycache__/errors.cpython-310.pyc,,
psycopg/__pycache__/generators.cpython-310.pyc,,
psycopg/__pycache__/postgres.cpython-310.pyc,,
psycopg/__pycache__/raw_cursor.cpython-310.pyc,,
psycopg/__pycache__/rows.cpython-310.pyc,,
psycopg/__pycache__/sql.cpython-310.pyc,,
psycopg/__pycache__/transaction.cpython-310.pyc,,
psycopg/__pycache__/version.cpython-310.pyc,,
psycopg/__pycache__/waiting.cpython-310.pyc,,
psycopg/_acompat.py,sha256=LvAVK1VjPjdSOcE7qNo9gaCFUoO6OxQsih3ts0EYrls,2684
psycopg/_adapters_map.py,sha256=jUyuu4KonltmJEkRmu_KXAFa7eegKG1w0iIKpSUz9Lk,10650
psycopg/_capabilities.py,sha256=d4Oqcqf_Jg2L2HbaPXDEz2vi-yc_q-ZEQs0xbi39hgM,4676
psycopg/_cmodule.py,sha256=17tVFnmf-azihlOuO0_1HdPDtXjUNf7b9Wonzae0ABE,872
psycopg/_column.py,sha256=wm8J8s42f6a9-X9BLR5vsd7LDS49M-__Bo4t1_CsZt0,2999
psycopg/_compat.py,sha256=pyQaSBzS_5qFOQa6c9JekevX2pjvKqIptR0xNETV1lY,1745
psycopg/_connection_base.py,sha256=BHr6xgGd6fHTMT4cNawddtaphbUNndlhRIFL_LjPZFs,23947
psycopg/_connection_info.py,sha256=FoytwL0rn6UbMx7hcnpG0vbNc_ZfH7L637G7AFMxBI0,5932
psycopg/_conninfo_attempts.py,sha256=1DAvMeajjCsLJUchEZ3-EGu0kOK-sV2Ol0TWyDrwdno,3465
psycopg/_conninfo_attempts_async.py,sha256=ec6Goz51lzJvtLFY7qxrRqG1jBPskwSKpmHtGkpy4VM,3554
psycopg/_conninfo_utils.py,sha256=AIvaAs3Qp8ewKfz87QG6n9u9Z9oOhi8QiTBseZsBNXU,3164
psycopg/_copy.py,sha256=YSK8EcsPmrT0YGwCxhnc08QTuIjdAhlGAqhaPawT-KY,9676
psycopg/_copy_async.py,sha256=TxRdQRV_PSVkVFTboQJ3POfyQS94dz92GEmipd6KWak,9915
psycopg/_copy_base.py,sha256=_3Bxem3du3C8cN4vD_x2R54FR5xyGpzPZOVLn6UOo9I,14032
psycopg/_cursor_base.py,sha256=juWrGpakYdbt5U23HYgHmWVvX8kWfSPeT3Gu8yDmWRM,21664
psycopg/_dns.py,sha256=K_0T1S2cjRkRCYhoE4i2LQpAENtBRk-APa52HLHomK0,7827
psycopg/_encodings.py,sha256=PRN945V8suh0oimDwszMGhhbm-P9KzuC6VRYo_fTi_c,4073
psycopg/_enums.py,sha256=5gCef_sYe9UdaE4n_RmDWeF4TfQOwQFeikjrkz9YivI,1691
psycopg/_oids.py,sha256=mJI8HbRrTsbK0LIgnnG5xPH-3hxNUclotqUurmyo2s0,1875
psycopg/_pipeline.py,sha256=FEiGRljO-nruIKYbtyVfb8SbgywcN3tSB4nkbLcOnxo,2165
psycopg/_pipeline_async.py,sha256=r5yahnA4JBkymx9TUqwa91mUlZynwLMqU4X9-lA3wl8,2108
psycopg/_pipeline_base.py,sha256=j4koc2RfLQg7VRcb_16yyOxfBwEst8aXFclgtzIBOeA,6666
psycopg/_preparing.py,sha256=uGelYVM3oE0h0vcmDg8q18j6BO_aThuwYAU5yG4iZsI,6279
psycopg/_py_transformer.py,sha256=V0qsNFHgjtvVU5IXmum22KcFXt9QUxGknsfr9sS2nhE,11711
psycopg/_queries.py,sha256=LhIYKwJGvD2EjNZ8PafkOp4opf_TDF99sUh-4HkjcgE,13532
psycopg/_server_cursor.py,sha256=vrXyv8zvV8kS2emkrx2aQa8XLnPJnVHTzsaNQZ7KNAI,4240
psycopg/_server_cursor_async.py,sha256=uQbdeIiWh5Pk8fp3tqV9m2m_OkdApH-Yk9T9lZiSR24,4305
psycopg/_server_cursor_base.py,sha256=d8SnNvgYet4k5ufiOKL_pnnoL1PtdgVgPUGgydA0lks,6989
psycopg/_struct.py,sha256=hP-pPmW9mjlfgIUJ0t-hdXxX4zSoC_lVgqpPLBOqAQM,1997
psycopg/_tpc.py,sha256=NLifIpdWphIbwaBPvLPSaZ_-Dote67yU67e9U0AokeQ,3529
psycopg/_transformer.py,sha256=mDkBLuSxbXDG1TckPgIyZJx00yn5UXU8DluOGmHWjAY,451
psycopg/_typeinfo.py,sha256=3ztADr7CeUNEOE1Lg03LKcWhiahbWVqKz-SN1mqto8s,10620
psycopg/_typemod.py,sha256=UZwHFfnWbXqxZ9UIVBrJh36599poZAaZj65v8EsbIJs,2498
psycopg/_tz.py,sha256=mIa3blpVVnfLVEu0v0Q1-TZrJ0B9-A-dtFuDB-PlikU,1186
psycopg/_wrappers.py,sha256=wKZt_ar8QuRgEKC6tuwamYXCwB1P7IZQsmgYdVOlAhU,3126
psycopg/abc.py,sha256=9pI7_pnnsTI_4n6n4LT_9AbOxfsFnMSuW5m--Vj9a68,7695
psycopg/adapt.py,sha256=fZV7nGpqV2UKwixLu50k1fb_sg0um6SuV1rCGVn136M,5195
psycopg/client_cursor.py,sha256=nRo3bckcKj3nZGAcXzrsPP45hEMfaxlegVne9wMdaz8,2681
psycopg/connection.py,sha256=Am8Fg45hmLUqL3-y1y5gAp_uSTfy-oDU0rZAA4dyns8,18708
psycopg/connection_async.py,sha256=7Z_uEx_AtWgtfNcWQCMFF7S7qw0ah88TYe0u-GZczZs,20921
psycopg/conninfo.py,sha256=9ZxxOLPWABuNBp4MUml_SKQN2LOVyiCm9s7zXFvtajY,4471
psycopg/copy.py,sha256=_V72jp2DXr2THsqo9S6cahveMo0QuBl3fnigt0rGmbc,806
psycopg/crdb/__init__.py,sha256=Z0wRvsSC_CpaOBWfWCilwP0sN6A3-IVhQD5cldgm_dE,439
psycopg/crdb/__pycache__/__init__.cpython-310.pyc,,
psycopg/crdb/__pycache__/_types.cpython-310.pyc,,
psycopg/crdb/__pycache__/connection.cpython-310.pyc,,
psycopg/crdb/_types.py,sha256=EToMhVZiT26K3m1GVS5r0FEMO88tDLjArZRCGyOCV3o,7270
psycopg/crdb/connection.py,sha256=Ms-v5uEOfSOWKvdcZx7d3VTXXfi5oTy_GhTCI1Cg1wQ,2758
psycopg/cursor.py,sha256=R2CZ8GNtwt01inrjkNYVDy7pyulY3EqyZA0QacwVJUg,9185
psycopg/cursor_async.py,sha256=Vh5H3oDgNZjcrM49hhudwvx0ItLwS8MOXalqiLNwBEU,9465
psycopg/dbapi20.py,sha256=F6u9qghqczRfxDy6RiGZnmwlQAxHavlrWMu0y63Wx4w,3284
psycopg/errors.py,sha256=mnaqwynL_XIgo1cvK_iYhxLk3RtuW3p7zULU7mb5tJM,46315
psycopg/generators.py,sha256=n_P05FjxPdbJWXznjRX4qfwyqQwqdr3J6kmBEjtPj2M,12699
psycopg/postgres.py,sha256=EzmCsrAsDfxuCyj20o-HDYw51t53B_zOlkOcpGwKkTc,6413
psycopg/pq/__init__.py,sha256=ftrMTxD9o70RxfkPP8U3iok58-i2JL2Xolj_OtC50cc,3989
psycopg/pq/__pycache__/__init__.cpython-310.pyc,,
psycopg/pq/__pycache__/_debug.cpython-310.pyc,,
psycopg/pq/__pycache__/_enums.cpython-310.pyc,,
psycopg/pq/__pycache__/_pq_ctypes.cpython-310.pyc,,
psycopg/pq/__pycache__/abc.cpython-310.pyc,,
psycopg/pq/__pycache__/misc.cpython-310.pyc,,
psycopg/pq/__pycache__/pq_ctypes.cpython-310.pyc,,
psycopg/pq/_debug.py,sha256=8eOcINaIO9TXBRpJeYMvI97qpwPKtVb7hldSIUSN7tA,3074
psycopg/pq/_enums.py,sha256=-hoW2JNpCRGz38iaL9D0TrdAtUHIafuWFwJSsmRyIhY,6004
psycopg/pq/_pq_ctypes.py,sha256=9hb5QAz8gUPryKMpBRDgcaualbL4zi_P23oj-aFruqU,22234
psycopg/pq/_pq_ctypes.pyi,sha256=ZOSCdeneEj8-3ynAdQY44bgZvvevRquFPEtv6bhHZOQ,10254
psycopg/pq/abc.py,sha256=1J6kprz6RJgfbaCHpsLqFh1Nnd_RDbEjKkfWCsGfgj0,8047
psycopg/pq/misc.py,sha256=2Q68kOJ0XFVssUGaFBfbBcKsxMO9aUNVNPMRd7wRNhU,6731
psycopg/pq/pq_ctypes.py,sha256=DyYpn3XhwYMEX2dAjbTSsuKy9NBOl_kNJmoMR1lWSRA,42135
psycopg/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
psycopg/raw_cursor.py,sha256=8A7eRaWJixbQt4d_XPJQcJe9VDaoEWWBeiNSrPbsCLk,2202
psycopg/rows.py,sha256=cuCofTLOuTp6FZmgDv1OZDMR-dXzgwbkoOzgcXQ56Rk,7877
psycopg/sql.py,sha256=BublVnwsisP-gKRz2xI2lbVMQ9Jgn7EGMQZf7aXHuwE,16563
psycopg/transaction.py,sha256=9rs0odmX-ydKnyIZ0bntAo-SXfmzQd2p_QdmMFJyuK8,9244
psycopg/types/__init__.py,sha256=JPAkV4EfkXwdsr76_U1DsQV19c8aQNSB6kgud4bhXeM,181
psycopg/types/__pycache__/__init__.cpython-310.pyc,,
psycopg/types/__pycache__/array.cpython-310.pyc,,
psycopg/types/__pycache__/bool.cpython-310.pyc,,
psycopg/types/__pycache__/composite.cpython-310.pyc,,
psycopg/types/__pycache__/datetime.cpython-310.pyc,,
psycopg/types/__pycache__/enum.cpython-310.pyc,,
psycopg/types/__pycache__/hstore.cpython-310.pyc,,
psycopg/types/__pycache__/json.cpython-310.pyc,,
psycopg/types/__pycache__/multirange.cpython-310.pyc,,
psycopg/types/__pycache__/net.cpython-310.pyc,,
psycopg/types/__pycache__/none.cpython-310.pyc,,
psycopg/types/__pycache__/numeric.cpython-310.pyc,,
psycopg/types/__pycache__/numpy.cpython-310.pyc,,
psycopg/types/__pycache__/range.cpython-310.pyc,,
psycopg/types/__pycache__/shapely.cpython-310.pyc,,
psycopg/types/__pycache__/string.cpython-310.pyc,,
psycopg/types/__pycache__/uuid.cpython-310.pyc,,
psycopg/types/array.py,sha256=DWFxsOPIrwZ0ZT0GoLmdSbeD1DjGKLinTStNfS45W5E,15348
psycopg/types/bool.py,sha256=0GJQG-Cm5P5NgH8TfLNj9n_e5ckw42XnsRBinX0k45w,1163
psycopg/types/composite.py,sha256=UUPUAreR2U-IKv4TMPbtQKdWFDP5E7mBfOFLaz3-u1I,11897
psycopg/types/datetime.py,sha256=XYw7vtq-Wb0v5XoTqrRkU28r8__Jtj_J9fhwfBuUmiA,25504
psycopg/types/enum.py,sha256=o0RfjBPCRBOnbW-v8evDx7hPGfpdsfg1m5YD_72ZMC4,7179
psycopg/types/hstore.py,sha256=6IMhyLAvyObInZ2v8OUPWsctTtpNSKS3q9TjGEmmmXA,7060
psycopg/types/json.py,sha256=iwMtdPedub8TV4xneioP4Pt3PFcHvLLklArmlF1UUKc,9825
psycopg/types/multirange.py,sha256=6g3HDJ8abPhhvWQpHwfVxm-xI5ZbCmmngrTZ-OzGg00,17582
psycopg/types/net.py,sha256=SNCvgJHwxAuzKUSxnpncf6K9ohpIGejw3cgwmgTOUN4,6850
psycopg/types/none.py,sha256=gy_OJqE1YslJ2RIix0Eq3x0B8IPfoH-Gcc2sQMIO0Sw,666
psycopg/types/numeric.py,sha256=EQ4Ri_iTjx-zQVqQQSGAfy80Ehoq5YxkChpgpDnfy_8,15463
psycopg/types/numpy.py,sha256=NPLUbDFyNjKvNTP6dhoUu1q80t3eQdHrrnetKAg8lbg,3273
psycopg/types/range.py,sha256=tMj7FJ4rpDTX5DiciVAD1rGvooABx8uaEdB2sLMV6Gw,21411
psycopg/types/shapely.py,sha256=R6vt277ipOM5LHYap0DnKmgfJSX_buhZOGvRy_KQg9Y,2593
psycopg/types/string.py,sha256=kRrK1CKTmp4ldHIMpjjol_2ksYAb-pwMvcb6_ZZ3_AA,7629
psycopg/types/uuid.py,sha256=klh__3aqeOwl7fGhE_oYafY9zK9BZXlAmc84VKavRDs,1630
psycopg/version.py,sha256=JLWoprIsC5HVclyhn7LzVfVpNtZYU-UoLQwAGMEBlYc,232
psycopg/waiting.py,sha256=AFp2A8JALCQzo_fFClAXx6gjbrTb0rXLU75w99ih2pU,12663
