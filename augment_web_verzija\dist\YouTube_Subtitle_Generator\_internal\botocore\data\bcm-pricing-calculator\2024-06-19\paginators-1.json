{"pagination": {"ListBillEstimateCommitments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillEstimateInputCommitmentModifications": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillEstimateInputUsageModifications": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillEstimateLineItems": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillEstimates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillScenarioCommitmentModifications": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillScenarioUsageModifications": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListBillScenarios": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListWorkloadEstimateUsage": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}, "ListWorkloadEstimates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "items"}}}