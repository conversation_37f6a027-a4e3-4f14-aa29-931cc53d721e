# @generated by tools/pyi/gen_pyi.py from torch/_C/return_types.pyi.in
# mypy: allow-untyped-defs

from typing import Final, NoReturn
from typing_extensions import Self

from torch import SymInt, Tensor
from torch.types import (  # noqa: F401
    _bool,
    _device,
    _dtype,
    _float,
    _int,
    _layout,
    _qscheme,
    _size,
    Number,
)

__all__ = [
    "pytree_register_structseq",
    "all_return_types",
    "_fake_quantize_per_tensor_affine_cachemask_tensor_qparams",
    "_fused_moving_avg_obs_fq_helper",
    "_linalg_det",
    "_linalg_eigh",
    "_linalg_slogdet",
    "_linalg_solve_ex",
    "_linalg_svd",
    "_lu_with_info",
    "_scaled_dot_product_cudnn_attention",
    "_scaled_dot_product_efficient_attention",
    "_scaled_dot_product_flash_attention",
    "_scaled_dot_product_flash_attention_for_cpu",
    "_unpack_dual",
    "aminmax",
    "cummax",
    "cummin",
    "frexp",
    "geqrf",
    "histogram",
    "histogramdd",
    "kthvalue",
    "lu_unpack",
    "max",
    "median",
    "min",
    "mode",
    "nanmedian",
    "qr",
    "slogdet",
    "sort",
    "svd",
    "topk",
    "triangular_solve",
]

def pytree_register_structseq(cls: type) -> None: ...

class _fake_quantize_per_tensor_affine_cachemask_tensor_qparams(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def output(self) -> Tensor: ...
    @property
    def mask(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _fused_moving_avg_obs_fq_helper(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def output(self) -> Tensor: ...
    @property
    def mask(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_det(tuple[Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def result(self) -> Tensor: ...
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 3
    n_sequence_fields: Final[_int] = 3
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_eigh(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def eigenvalues(self) -> Tensor: ...
    @property
    def eigenvectors(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_slogdet(tuple[Tensor, Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def sign(self) -> Tensor: ...
    @property
    def logabsdet(self) -> Tensor: ...
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 4
    n_sequence_fields: Final[_int] = 4
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_solve_ex(tuple[Tensor, Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def result(self) -> Tensor: ...
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    @property
    def info(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 4
    n_sequence_fields: Final[_int] = 4
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _linalg_svd(tuple[Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def U(self) -> Tensor: ...
    @property
    def S(self) -> Tensor: ...
    @property
    def Vh(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 3
    n_sequence_fields: Final[_int] = 3
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _lu_with_info(tuple[Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def LU(self) -> Tensor: ...
    @property
    def pivots(self) -> Tensor: ...
    @property
    def info(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 3
    n_sequence_fields: Final[_int] = 3
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_cudnn_attention(tuple[Tensor, Tensor, Tensor, Tensor, _int | SymInt, _int | SymInt, Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def output(self) -> Tensor: ...
    @property
    def logsumexp(self) -> Tensor: ...
    @property
    def cum_seq_q(self) -> Tensor: ...
    @property
    def cum_seq_k(self) -> Tensor: ...
    @property
    def max_q(self) -> _int | SymInt: ...
    @property
    def max_k(self) -> _int | SymInt: ...
    @property
    def philox_seed(self) -> Tensor: ...
    @property
    def philox_offset(self) -> Tensor: ...
    @property
    def debug_attn_mask(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor, Tensor, _int | SymInt, _int | SymInt, Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 9
    n_sequence_fields: Final[_int] = 9
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_efficient_attention(tuple[Tensor, Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def output(self) -> Tensor: ...
    @property
    def log_sumexp(self) -> Tensor: ...
    @property
    def philox_seed(self) -> Tensor: ...
    @property
    def philox_offset(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 4
    n_sequence_fields: Final[_int] = 4
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_flash_attention(tuple[Tensor, Tensor, Tensor, Tensor, _int | SymInt, _int | SymInt, Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def output(self) -> Tensor: ...
    @property
    def logsumexp(self) -> Tensor: ...
    @property
    def cum_seq_q(self) -> Tensor: ...
    @property
    def cum_seq_k(self) -> Tensor: ...
    @property
    def max_q(self) -> _int | SymInt: ...
    @property
    def max_k(self) -> _int | SymInt: ...
    @property
    def rng_state(self) -> Tensor: ...
    @property
    def unused(self) -> Tensor: ...
    @property
    def debug_attn_mask(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor, Tensor, _int | SymInt, _int | SymInt, Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 9
    n_sequence_fields: Final[_int] = 9
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _scaled_dot_product_flash_attention_for_cpu(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def output(self) -> Tensor: ...
    @property
    def logsumexp(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class _unpack_dual(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def primal(self) -> Tensor: ...
    @property
    def tangent(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class aminmax(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def min(self) -> Tensor: ...
    @property
    def max(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class cummax(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class cummin(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class frexp(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def mantissa(self) -> Tensor: ...
    @property
    def exponent(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class geqrf(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def a(self) -> Tensor: ...
    @property
    def tau(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class histogram(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def hist(self) -> Tensor: ...
    @property
    def bin_edges(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class histogramdd(tuple[Tensor, tuple[Tensor, ...]]):  # fmt: skip
    @property
    def hist(self) -> Tensor: ...
    @property
    def bin_edges(self) -> tuple[Tensor, ...]: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, tuple[Tensor, ...]],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class kthvalue(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class lu_unpack(tuple[Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def P(self) -> Tensor: ...
    @property
    def L(self) -> Tensor: ...
    @property
    def U(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 3
    n_sequence_fields: Final[_int] = 3
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class max(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class median(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class min(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class mode(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class nanmedian(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class qr(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def Q(self) -> Tensor: ...
    @property
    def R(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class slogdet(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def sign(self) -> Tensor: ...
    @property
    def logabsdet(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class sort(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class svd(tuple[Tensor, Tensor, Tensor]):  # fmt: skip
    @property
    def U(self) -> Tensor: ...
    @property
    def S(self) -> Tensor: ...
    @property
    def V(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 3
    n_sequence_fields: Final[_int] = 3
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class topk(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def values(self) -> Tensor: ...
    @property
    def indices(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

class triangular_solve(tuple[Tensor, Tensor]):  # fmt: skip
    @property
    def solution(self) -> Tensor: ...
    @property
    def cloned_coefficient(self) -> Tensor: ...
    def __new__(
        cls,
        sequence: tuple[Tensor, Tensor],
    ) -> Self:  # fmt: skip
        ...
    n_fields: Final[_int] = 2
    n_sequence_fields: Final[_int] = 2
    n_unnamed_fields: Final[_int] = 0
    def __init_subclass__(cls) -> NoReturn: ...  # prohibit subclassing

all_return_types: list[type] = ...
