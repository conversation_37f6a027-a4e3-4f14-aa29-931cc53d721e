{"service": {"actions": {"CreatePlatformApplication": {"request": {"operation": "CreatePlatformApplication"}, "resource": {"type": "PlatformApplication", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "PlatformApplicationArn"}]}}, "CreateTopic": {"request": {"operation": "CreateTopic"}, "resource": {"type": "Topic", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "TopicArn"}]}}}, "has": {"PlatformApplication": {"resource": {"type": "PlatformApplication", "identifiers": [{"target": "<PERSON><PERSON>", "source": "input"}]}}, "PlatformEndpoint": {"resource": {"type": "PlatformEndpoint", "identifiers": [{"target": "<PERSON><PERSON>", "source": "input"}]}}, "Subscription": {"resource": {"type": "Subscription", "identifiers": [{"target": "<PERSON><PERSON>", "source": "input"}]}}, "Topic": {"resource": {"type": "Topic", "identifiers": [{"target": "<PERSON><PERSON>", "source": "input"}]}}}, "hasMany": {"PlatformApplications": {"request": {"operation": "ListPlatformApplications"}, "resource": {"type": "PlatformApplication", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "PlatformApplications[].PlatformApplicationArn"}]}}, "Subscriptions": {"request": {"operation": "ListSubscriptions"}, "resource": {"type": "Subscription", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "Subscriptions[].SubscriptionArn"}]}}, "Topics": {"request": {"operation": "ListTopics"}, "resource": {"type": "Topic", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "Topics[].TopicArn"}]}}}}, "resources": {"PlatformApplication": {"identifiers": [{"name": "<PERSON><PERSON>"}], "shape": "GetPlatformApplicationAttributesResponse", "load": {"request": {"operation": "GetPlatformApplicationAttributes", "params": [{"target": "PlatformApplicationArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "path": "@"}, "actions": {"CreatePlatformEndpoint": {"request": {"operation": "CreatePlatformEndpoint", "params": [{"target": "PlatformApplicationArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "resource": {"type": "PlatformEndpoint", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "EndpointArn"}]}}, "Delete": {"request": {"operation": "DeletePlatformApplication", "params": [{"target": "PlatformApplicationArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "SetAttributes": {"request": {"operation": "SetPlatformApplicationAttributes", "params": [{"target": "PlatformApplicationArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}}, "hasMany": {"Endpoints": {"request": {"operation": "ListEndpointsByPlatformApplication", "params": [{"target": "PlatformApplicationArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "resource": {"type": "PlatformEndpoint", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "Endpoints[].EndpointArn"}]}}}}, "PlatformEndpoint": {"identifiers": [{"name": "<PERSON><PERSON>"}], "shape": "GetEndpointAttributesResponse", "load": {"request": {"operation": "GetEndpointAttributes", "params": [{"target": "EndpointArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "path": "@"}, "actions": {"Delete": {"request": {"operation": "DeleteEndpoint", "params": [{"target": "EndpointArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "Publish": {"request": {"operation": "Publish", "params": [{"target": "TargetArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "SetAttributes": {"request": {"operation": "SetEndpointAttributes", "params": [{"target": "EndpointArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}}}, "Subscription": {"identifiers": [{"name": "<PERSON><PERSON>"}], "shape": "GetSubscriptionAttributesResponse", "load": {"request": {"operation": "GetSubscriptionAttributes", "params": [{"target": "SubscriptionArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "path": "@"}, "actions": {"Delete": {"request": {"operation": "Unsubscribe", "params": [{"target": "SubscriptionArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "SetAttributes": {"request": {"operation": "SetSubscriptionAttributes", "params": [{"target": "SubscriptionArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}}}, "Topic": {"identifiers": [{"name": "<PERSON><PERSON>"}], "shape": "GetTopicAttributesResponse", "load": {"request": {"operation": "GetTopicAttributes", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "path": "@"}, "actions": {"AddPermission": {"request": {"operation": "AddPermission", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "ConfirmSubscription": {"request": {"operation": "ConfirmSubscription", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "resource": {"type": "Subscription", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "SubscriptionArn"}]}}, "Delete": {"request": {"operation": "DeleteTopic", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "Publish": {"request": {"operation": "Publish", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "RemovePermission": {"request": {"operation": "RemovePermission", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "SetAttributes": {"request": {"operation": "SetTopicAttributes", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}}, "Subscribe": {"request": {"operation": "Subscribe", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "resource": {"type": "Subscription", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "SubscriptionArn"}]}}}, "hasMany": {"Subscriptions": {"request": {"operation": "ListSubscriptionsByTopic", "params": [{"target": "TopicArn", "source": "identifier", "name": "<PERSON><PERSON>"}]}, "resource": {"type": "Subscription", "identifiers": [{"target": "<PERSON><PERSON>", "source": "response", "path": "Subscriptions[].SubscriptionArn"}]}}}}}}