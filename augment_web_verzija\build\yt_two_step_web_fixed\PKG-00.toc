('E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\YouTube_Subtitle_Generator.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\PYZ-00.pyz',
   'PYZ'),
  ('struct',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\localpycs\\struct.pyc',
   'PYMODULE'),
  ('pyimod01_archive',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\build\\yt_two_step_web_fixed\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'E:\\laragon\\bin\\python\\python-3.10\\lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_mplconfig',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_mplconfig.py',
   'PYSOURCE'),
  ('pyi_rth__tkinter',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth__tkinter.py',
   'PYSOURCE'),
  ('pyi_rth_nltk',
   'E:\\laragon\\bin\\python\\python-3.10\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_nltk.py',
   'PYSOURCE'),
  ('yt_two_step_web',
   'E:\\laragon\\www\\yt_to_text\\augment_web_verzija\\yt_two_step_web.py',
   'PYSOURCE')],
 'python310.dll',
 True,
 False,
 False,
 [],
 None,
 None,
 None)
